# Definition of singly linked list:
class ListNode:
    def __init__(self, val=0, next=None):
        self.val = val
        self.next = next

class Solution:
    def reverseList(self, head):
        # Handle edge cases: empty list or single node
        if not head:
            return None
        if head.next is None:
            return head
        
        # Initialize pointers for reversing
        prev = None
        current = head
        
        # Reverse the list by changing pointers
        while current:
            # Save the next node before we change the pointer
            next_temp = current.next
            
            # Reverse the pointer to point to the previous node
            current.next = prev
            
            # Move prev and current one step forward
            prev = current
            current = next_temp
        
        # At the end, prev will be pointing to the new head
        return prev

# Test the solution
if __name__ == "__main__":
    # Create a sample linked list: 1->2->3->4->5
    head = ListNode(1)
    head.next = ListNode(2)
    head.next.next = ListNode(3)
    head.next.next.next = ListNode(4)
    head.next.next.next.next = ListNode(5)
    
    # Print the original list
    def print_list(node):
        values = []
        while node:
            values.append(str(node.val))
            node = node.next
        return "->".join(values)
    
    print("Original list:", print_list(head))
    
    # Reverse the list
    solution = Solution()
    reversed_head = solution.reverseList(head)
    
    # Print the reversed list
    print("Reversed list:", print_list(reversed_head))
